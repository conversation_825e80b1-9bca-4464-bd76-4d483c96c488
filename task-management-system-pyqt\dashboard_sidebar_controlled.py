import sys
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFrame, QStackedWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import <PERSON><PERSON>ont, QPalette, QColor

# Import des composants
from chart_widgets import MetricCard
from bureau_task_manager import BureauTaskManager

class ModernButton(QPushButton):
    """Bouton moderne avec couleurs personnalisées et état sélectionné"""
    
    def __init__(self, text, color, parent=None):
        super().__init__(text, parent)
        self.color = color
        self.is_selected = False
        self.setFixedHeight(50)
        self.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.setup_style()
        
    def setup_style(self):
        border_style = "border-left: 4px solid #00D4FF;" if self.is_selected else ""
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border: none;
                border-radius: 12px;
                color: white;
                text-align: left;
                padding-left: 20px;
                margin: 5px;
                {border_style}
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.lighten_color(self.color)}, stop:1 {self.color});
            }}
            QPushButton:pressed {{
                background: {self.darken_color(self.color)};
            }}
        """)
    
    def set_selected(self, selected):
        """Marque le bouton comme sélectionné"""
        self.is_selected = selected
        self.setup_style()
    
    def darken_color(self, color):
        color_map = {
            "#FF6B6B": "#E55555", "#4ECDC4": "#3BB5AE", "#45B7D1": "#3A9BC1",
            "#96CEB4": "#7FB89A", "#FECA57": "#E6B547", "#FF9FF3": "#E68FD3",
            "#54A0FF": "#4490EF", "#5F27CD": "#4F1FAD"
        }
        return color_map.get(color, color)
    
    def lighten_color(self, color):
        color_map = {
            "#FF6B6B": "#FF7B7B", "#4ECDC4": "#5EDDD4", "#45B7D1": "#55C7E1",
            "#96CEB4": "#A6DEC4", "#FECA57": "#FEDA67", "#FF9FF3": "#FFAFF3",
            "#54A0FF": "#64B0FF", "#5F27CD": "#6F37DD"
        }
        return color_map.get(color, color)

class BureauInfoPanel(QWidget):
    """Panneau d'information pour un bureau spécifique"""
    
    def __init__(self, bureau_name, parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête du bureau
        header = QLabel(f"Bureau {self.bureau_name}")
        header.setFont(QFont("Segoe UI", 20, QFont.Bold))
        header.setStyleSheet("color: white; margin-bottom: 20px;")
        
        # Cartes de statistiques spécifiques au bureau
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(15)
        stats_layout.setContentsMargins(0, 0, 0, 0)

        # Statistiques dynamiques basées sur le bureau
        stats = self.get_bureau_stats()

        completed_card = MetricCard("Tâches Complétées", stats['completed'], stats['completed_trend'], "progress")
        in_progress_card = MetricCard("En Cours", stats['in_progress'], stats['progress_trend'], "progress")
        upcoming_card = MetricCard("Performance", stats['performance'], stats['perf_trend'], "line")

        # Assurer que les cartes ont la même taille
        for card in [completed_card, in_progress_card, upcoming_card]:
            card.setMinimumHeight(160)
            card.setMaximumHeight(180)

        stats_layout.addWidget(completed_card, 1)
        stats_layout.addWidget(in_progress_card, 1)
        stats_layout.addWidget(upcoming_card, 1)
        
        # Description du bureau
        description = QLabel(self.get_bureau_description())
        description.setFont(QFont("Segoe UI", 12))
        description.setStyleSheet("""
            color: #B0B0B0; 
            background-color: #2C2C54; 
            padding: 15px; 
            border-radius: 10px;
            margin-top: 20px;
        """)
        description.setWordWrap(True)
        
        layout.addWidget(header)
        layout.addLayout(stats_layout)
        layout.addWidget(description)
        layout.addStretch()
    
    def get_bureau_stats(self):
        """Retourne des statistiques spécifiques au bureau"""
        bureau_stats = {
            "1B": {"completed": "18", "completed_trend": "↑ 15%", "in_progress": "5", "progress_trend": "↓ 2", "performance": "92%", "perf_trend": "↑ 8%"},
            "2B": {"completed": "24", "completed_trend": "↑ 12%", "in_progress": "8", "progress_trend": "↑ 3", "performance": "89%", "perf_trend": "↑ 5%"},
            "3B": {"completed": "16", "completed_trend": "↑ 20%", "in_progress": "6", "progress_trend": "→ 0", "performance": "85%", "perf_trend": "↑ 3%"},
            "4B": {"completed": "22", "completed_trend": "↑ 18%", "in_progress": "4", "progress_trend": "↓ 1", "performance": "94%", "perf_trend": "↑ 6%"},
            "5B": {"completed": "20", "completed_trend": "↑ 10%", "in_progress": "7", "progress_trend": "↑ 2", "performance": "88%", "perf_trend": "↑ 4%"},
            "B.GENIE": {"completed": "12", "completed_trend": "↑ 25%", "in_progress": "3", "progress_trend": "→ 0", "performance": "96%", "perf_trend": "↑ 12%"},
            "C.TRANS": {"completed": "28", "completed_trend": "↑ 14%", "in_progress": "9", "progress_trend": "↑ 4", "performance": "91%", "perf_trend": "↑ 7%"},
            "SECRETARIAT": {"completed": "35", "completed_trend": "↑ 22%", "in_progress": "12", "progress_trend": "↑ 5", "performance": "87%", "perf_trend": "↑ 9%"}
        }
        return bureau_stats.get(self.bureau_name, {"completed": "0", "completed_trend": "→ 0%", "in_progress": "0", "progress_trend": "→ 0", "performance": "0%", "perf_trend": "→ 0%"})
    
    def get_bureau_description(self):
        """Retourne une description spécifique au bureau"""
        descriptions = {
            "1B": "📊 Bureau 1B - Gestion Administrative\n\n• Traitement des dossiers administratifs\n• Gestion des courriers entrants\n• Suivi des procédures internes\n• Coordination avec les autres services",
            "2B": "📋 Bureau 2B - Ressources Humaines\n\n• Gestion du personnel\n• Recrutement et formation\n• Suivi des congés et absences\n• Relations sociales",
            "3B": "💰 Bureau 3B - Finances et Comptabilité\n\n• Gestion budgétaire\n• Comptabilité générale\n• Suivi des factures\n• Contrôle financier",
            "4B": "🏗️ Bureau 4B - Travaux et Infrastructure\n\n• Maintenance des bâtiments\n• Gestion des travaux\n• Suivi des équipements\n• Sécurité des installations",
            "5B": "📞 Bureau 5B - Communication\n\n• Relations publiques\n• Communication interne\n• Gestion des médias\n• Événements et protocole",
            "B.GENIE": "🧠 Bureau GENIE - Innovation et Technologie\n\n• Développement technologique\n• Innovation numérique\n• Recherche et développement\n• Transformation digitale",
            "C.TRANS": "🚚 Centre TRANS - Transport et Logistique\n\n• Gestion du parc automobile\n• Logistique et approvisionnement\n• Transport du personnel\n• Maintenance des véhicules",
            "SECRETARIAT": "📋 Secrétariat Général\n\n• Coordination générale\n• Gestion des réunions\n• Suivi des décisions\n• Interface avec la direction"
        }
        return descriptions.get(self.bureau_name, f"Bureau {self.bureau_name} - Description non disponible")

class SidebarControlledDashboard(QMainWindow):
    """Dashboard avec sidebar qui contrôle l'affichage des tâches par bureau"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Dashboard - Navigation par Bureau")
        self.setGeometry(100, 100, 1400, 800)
        
        # Créer un gestionnaire de tâches global
        self.task_manager = BureauTaskManager()
        
        # Variables pour la navigation
        self.current_bureau_index = 0
        self.bureaux_names = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        
        self.setup_ui()
        self.apply_dark_theme()

        # Aucun bureau sélectionné par défaut - l'utilisateur doit cliquer sur la sidebar
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar
        self.create_sidebar(main_layout)
        
        # Zone de contenu principal divisée
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # Panneau d'information du bureau (en haut)
        self.bureau_info_stack = QStackedWidget()
        self.create_bureau_info_panels()
        self.bureau_info_stack.setMinimumHeight(320)
        self.bureau_info_stack.setMaximumHeight(350)
        
        # Gestionnaire de tâches (en bas)
        task_frame = QFrame()
        task_frame.setStyleSheet("""
            QFrame {
                background-color: #2C2C54;
                border-radius: 15px;
                margin-top: 10px;
            }
        """)
        task_layout = QVBoxLayout(task_frame)
        task_layout.setContentsMargins(10, 10, 10, 10)
        
        # Pas de titre - les tâches sont directement affichées selon le bureau sélectionné
        task_layout.addWidget(self.task_manager)
        
        content_layout.addWidget(self.bureau_info_stack)
        content_layout.addWidget(task_frame)
        
        main_layout.addWidget(content_widget, 1)
        
    def create_sidebar(self, main_layout):
        """Crée la sidebar avec les boutons colorés"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1A1A2E, stop:1 #16213E);
                border-right: 1px solid #404040;
            }
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(15, 20, 15, 20)
        sidebar_layout.setSpacing(10)
        
        # Logo/Titre
        title = QLabel("TASK MANAGER")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("color: white; margin-bottom: 30px; text-align: center;")
        title.setAlignment(Qt.AlignCenter)
        
        sidebar_layout.addWidget(title)
        
        # Boutons de navigation
        self.nav_buttons = []
        bureaux = [
            ("🏢 1B", "#FF6B6B"),
            ("🏢 2B", "#4ECDC4"), 
            ("🏢 3B", "#45B7D1"),
            ("🏢 4B", "#96CEB4"),
            ("🏢 5B", "#FECA57"),
            ("🧠 B.GENIE", "#FF9FF3"),
            ("🚚 C.TRANS", "#54A0FF"),
            ("📋 SECRETARIAT", "#5F27CD")
        ]
        
        for i, (name, color) in enumerate(bureaux):
            btn = ModernButton(name, color)
            btn.clicked.connect(lambda checked, idx=i: self.switch_to_bureau(idx))
            self.nav_buttons.append(btn)
            sidebar_layout.addWidget(btn)
        
        sidebar_layout.addStretch()
        
        # Bouton de déconnexion
        logout_btn = ModernButton("🚪 Déconnexion", "#E74C3C")
        sidebar_layout.addWidget(logout_btn)
        
        main_layout.addWidget(sidebar)
        
    def create_bureau_info_panels(self):
        """Crée les panneaux d'information pour chaque bureau"""
        for bureau in self.bureaux_names:
            panel = BureauInfoPanel(bureau)
            self.bureau_info_stack.addWidget(panel)
    
    def switch_to_bureau(self, index):
        """Bascule vers le bureau sélectionné et affiche ses tâches"""
        if 0 <= index < len(self.bureaux_names):
            self.current_bureau_index = index
            bureau_name = self.bureaux_names[index]
            
            # Mettre à jour l'affichage du panneau d'information
            self.bureau_info_stack.setCurrentIndex(index)
            
            # Basculer vers l'onglet du bureau dans le gestionnaire de tâches
            self.task_manager.switch_to_bureau(bureau_name)
            
            # Mettre à jour l'état des boutons
            for i, btn in enumerate(self.nav_buttons):
                btn.set_selected(i == index)
            
            # Mettre à jour le titre de la fenêtre
            self.setWindowTitle(f"Dashboard - Bureau {bureau_name}")
    
    def apply_dark_theme(self):
        """Applique le thème sombre moderne"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0F0F23;
                color: white;
            }
            QWidget {
                background-color: transparent;
            }
        """)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setStyle("Fusion")
    
    # Palette sombre
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(15, 15, 35))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    app.setPalette(palette)
    
    dashboard = SidebarControlledDashboard()
    dashboard.show()
    
    sys.exit(app.exec_())
