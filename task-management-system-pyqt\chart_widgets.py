import sys
import math
from PyQt5.QtWidgets import <PERSON><PERSON>idget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QLinearGradient, QConicalGradient

class CircularProgressBar(QWidget):
    """Barre de progression circulaire moderne"""

    def __init__(self, value=0, max_value=100, color="#4ECDC4", parent=None):
        super().__init__(parent)
        self._value = value
        self.max_value = max_value
        self.color = QColor(color)
        self.setFixedSize(120, 120)

        # Animation désactivée temporairement pour éviter les erreurs
        # self.animation = QPropertyAnimation(self, b"value")
        # self.animation.setDuration(1500)
        # self.animation.setEasingCurve(QEasingCurve.OutCubic)

    @property
    def value(self):
        return self._value

    @value.setter
    def value(self, val):
        self._value = val
        self.update()

    def set_value(self, value):
        """Définit une nouvelle valeur"""
        self._value = value
        self.update()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Dimensions
        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 2 - 10
        
        # Cercle de fond
        painter.setPen(QPen(QColor("#2C2C54"), 8))
        painter.drawEllipse(center.x() - radius, center.y() - radius, 
                          radius * 2, radius * 2)
        
        # Cercle de progression
        progress_angle = int((self._value / self.max_value) * 360 * 16)
        
        # Gradient pour le cercle de progression
        gradient = QConicalGradient(center, 0)
        gradient.setColorAt(0, self.color)
        gradient.setColorAt(1, self.color.lighter(150))
        
        painter.setPen(QPen(QBrush(gradient), 8, Qt.SolidLine, Qt.RoundCap))
        painter.drawArc(center.x() - radius, center.y() - radius,
                       radius * 2, radius * 2, 90 * 16, -progress_angle)
        
        # Texte au centre
        painter.setPen(QColor("white"))
        painter.setFont(QFont("Segoe UI", 16, QFont.Bold))
        painter.drawText(rect, Qt.AlignCenter, f"{int(self._value)}%")

class LineChart(QWidget):
    """Graphique linéaire moderne"""
    
    def __init__(self, data=None, color="#45B7D1", parent=None):
        super().__init__(parent)
        self.data = data or [20, 35, 30, 45, 40, 55, 50, 65, 60, 70]
        self.color = QColor(color)
        self.setMinimumSize(300, 150)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        margin = 20
        
        # Zone de dessin
        draw_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        if len(self.data) < 2:
            return
            
        # Calcul des points
        max_val = max(self.data)
        min_val = min(self.data)
        val_range = max_val - min_val if max_val != min_val else 1
        
        points = []
        for i, val in enumerate(self.data):
            x = draw_rect.left() + (i / (len(self.data) - 1)) * draw_rect.width()
            y = draw_rect.bottom() - ((val - min_val) / val_range) * draw_rect.height()
            points.append((x, y))
        
        # Gradient de fond
        gradient = QLinearGradient(0, draw_rect.top(), 0, draw_rect.bottom())
        gradient.setColorAt(0, QColor(self.color.red(), self.color.green(), 
                                    self.color.blue(), 100))
        gradient.setColorAt(1, QColor(self.color.red(), self.color.green(), 
                                    self.color.blue(), 20))
        
        # Dessiner la zone sous la courbe
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        
        from PyQt5.QtGui import QPolygonF
        from PyQt5.QtCore import QPointF
        
        polygon = QPolygonF()
        polygon.append(QPointF(points[0][0], draw_rect.bottom()))
        for x, y in points:
            polygon.append(QPointF(x, y))
        polygon.append(QPointF(points[-1][0], draw_rect.bottom()))
        
        painter.drawPolygon(polygon)
        
        # Dessiner la ligne
        painter.setPen(QPen(self.color, 3))
        for i in range(len(points) - 1):
            painter.drawLine(int(points[i][0]), int(points[i][1]),
                           int(points[i+1][0]), int(points[i+1][1]))
        
        # Dessiner les points
        painter.setBrush(QBrush(self.color))
        painter.setPen(QPen(QColor("white"), 2))
        for x, y in points:
            painter.drawEllipse(int(x-4), int(y-4), 8, 8)

class DonutChart(QWidget):
    """Graphique en donut moderne"""
    
    def __init__(self, data=None, colors=None, parent=None):
        super().__init__(parent)
        self.data = data or [30, 25, 20, 15, 10]
        self.colors = colors or ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FECA57"]
        self.labels = ["1B", "2B", "3B", "4B", "5B"]
        self.setFixedSize(200, 200)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        center = rect.center()
        outer_radius = min(rect.width(), rect.height()) // 2 - 10
        inner_radius = outer_radius - 30
        
        # Calcul des angles
        total = sum(self.data)
        start_angle = 0
        
        for i, (value, color) in enumerate(zip(self.data, self.colors)):
            angle = int((value / total) * 360 * 16)
            
            # Gradient pour chaque section
            gradient = QConicalGradient(center, start_angle / 16)
            gradient.setColorAt(0, QColor(color))
            gradient.setColorAt(1, QColor(color).lighter(120))
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor("#1A1A2E"), 2))
            
            # Dessiner l'arc
            painter.drawPie(center.x() - outer_radius, center.y() - outer_radius,
                          outer_radius * 2, outer_radius * 2,
                          start_angle, angle)
            
            start_angle += angle
        
        # Cercle intérieur pour créer l'effet donut
        painter.setBrush(QBrush(QColor("#1A1A2E")))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(center.x() - inner_radius, center.y() - inner_radius,
                          inner_radius * 2, inner_radius * 2)
        
        # Texte au centre
        painter.setPen(QColor("white"))
        painter.setFont(QFont("Segoe UI", 12, QFont.Bold))
        painter.drawText(rect, Qt.AlignCenter, f"{total}\nTâches")

class BarChart(QWidget):
    """Graphique en barres moderne"""
    
    def __init__(self, data=None, labels=None, color="#54A0FF", parent=None):
        super().__init__(parent)
        self.data = data or [45, 60, 35, 80, 55, 70, 40]
        self.labels = labels or ["Lun", "Mar", "Mer", "Jeu", "Ven", "Sam", "Dim"]
        self.color = QColor(color)
        self.setMinimumSize(300, 200)
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        margin = 30
        draw_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        if not self.data:
            return
            
        max_val = max(self.data)
        bar_width = draw_rect.width() / len(self.data) * 0.7
        spacing = draw_rect.width() / len(self.data) * 0.3
        
        for i, (value, label) in enumerate(zip(self.data, self.labels)):
            # Position de la barre
            x = draw_rect.left() + i * (bar_width + spacing) + spacing / 2
            height = (value / max_val) * draw_rect.height()
            y = draw_rect.bottom() - height
            
            # Gradient pour la barre
            gradient = QLinearGradient(0, y, 0, y + height)
            gradient.setColorAt(0, self.color.lighter(130))
            gradient.setColorAt(1, self.color)
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(int(x), int(y), int(bar_width), int(height), 5, 5)
            
            # Label
            painter.setPen(QColor("#B0B0B0"))
            painter.setFont(QFont("Segoe UI", 8))
            text_rect = rect.adjusted(int(x), draw_rect.bottom() + 5, 
                                    int(x + bar_width), rect.bottom())
            painter.drawText(text_rect, Qt.AlignCenter, label)

class MetricCard(QFrame):
    """Carte de métrique avec graphique intégré"""

    def __init__(self, title, value, change, chart_type="progress", parent=None):
        super().__init__(parent)
        # Taille flexible pour éviter les coupures
        self.setMinimumSize(250, 140)
        self.setMaximumSize(350, 200)
        self.setSizePolicy(self.sizePolicy().Expanding, self.sizePolicy().Fixed)
        self.setup_ui(title, value, change, chart_type)
        
    def setup_ui(self, title, value, change, chart_type):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Partie gauche - Texte
        text_layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10))
        title_label.setStyleSheet("color: #B0B0B0;")
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        value_label.setStyleSheet("color: white;")
        
        change_label = QLabel(change)
        change_label.setFont(QFont("Segoe UI", 9))
        change_color = "#4ECDC4" if "↑" in change else "#FF6B6B"
        change_label.setStyleSheet(f"color: {change_color};")
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(value_label)
        text_layout.addWidget(change_label)
        text_layout.addStretch()
        
        # Partie droite - Graphique
        if chart_type == "progress":
            chart = CircularProgressBar(75, 100, "#4ECDC4")
        elif chart_type == "line":
            chart = LineChart([20, 35, 30, 45, 40, 55], "#45B7D1")
            chart.setFixedSize(100, 60)
        else:
            chart = CircularProgressBar(60, 100, "#FF6B6B")
        
        layout.addLayout(text_layout)
        layout.addWidget(chart)
        
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2C2C54, stop:1 #1A1A2E);
                border-radius: 15px;
                border: 1px solid #404040;
            }
        """)

if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication, QMainWindow, QGridLayout, QWidget
    
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("Composants de Graphiques Modernes")
    window.setGeometry(100, 100, 800, 600)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    layout = QGridLayout(central_widget)
    
    # Test des composants
    layout.addWidget(MetricCard("Tâches Complétées", "156", "↑ 12%", "progress"), 0, 0)
    layout.addWidget(MetricCard("Performance", "89%", "↑ 5%", "line"), 0, 1)
    layout.addWidget(LineChart(), 1, 0, 1, 2)
    layout.addWidget(DonutChart(), 2, 0)
    layout.addWidget(BarChart(), 2, 1)
    
    # Style sombre
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
        }
    """)
    
    window.show()
    sys.exit(app.exec_())
