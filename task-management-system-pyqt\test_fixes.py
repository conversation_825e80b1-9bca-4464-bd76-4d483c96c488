#!/usr/bin/env python3
"""
Script de test pour vérifier les corrections apportées au dashboard.

Problèmes corrigés :
1. Coupure dans les MetricCard - Tailles ajustées et layout amélioré
2. Boutons d'actions manquants - Style et taille corrigés
3. Erreurs d'animation - Animation désactivée temporairement
4. Hauteur des lignes du tableau - Ajustée pour les boutons
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# Import des composants corrigés
from chart_widgets import MetricCard, CircularProgressBar, LineChart
from bureau_task_manager import BureauTaskManager, BureauTaskTable
from dashboard_sidebar_controlled import SidebarControlledDashboard

def test_metric_cards():
    """Test des MetricCard pour vérifier qu'elles ne sont plus coupées"""
    print("🧪 Test des MetricCard...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Créer une fenêtre de test
    window = QMainWindow()
    window.setWindowTitle("Test MetricCard - Vérification des coupures")
    window.setGeometry(100, 100, 1000, 400)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # Titre
    title = QLabel("Test des MetricCard - Vérification des tailles")
    title.setFont(QFont("Segoe UI", 16, QFont.Bold))
    title.setAlignment(Qt.AlignCenter)
    layout.addWidget(title)
    
    # Créer plusieurs MetricCard pour tester
    from PyQt5.QtWidgets import QHBoxLayout
    cards_layout = QHBoxLayout()
    
    card1 = MetricCard("Tâches Complétées", "156", "↑ 12%", "progress")
    card2 = MetricCard("En Cours", "24", "↑ 3", "progress")
    card3 = MetricCard("Performance", "89%", "↑ 5%", "line")
    
    cards_layout.addWidget(card1)
    cards_layout.addWidget(card2)
    cards_layout.addWidget(card3)
    
    layout.addLayout(cards_layout)
    
    # Style sombre
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
            color: white;
        }
        QLabel {
            color: white;
            margin: 20px;
        }
    """)
    
    window.show()
    print("✅ MetricCard créées avec succès - Vérifiez visuellement qu'elles ne sont pas coupées")
    return window

def test_task_table():
    """Test du tableau de tâches pour vérifier les boutons d'actions"""
    print("🧪 Test du tableau de tâches...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Créer une fenêtre de test
    window = QMainWindow()
    window.setWindowTitle("Test Tableau - Vérification des boutons d'actions")
    window.setGeometry(200, 200, 1200, 600)
    
    # Créer un tableau de test
    table = BureauTaskTable("1B")
    
    # Ajouter quelques tâches de test
    test_tasks = [
        {
            "title": "Tâche de test 1",
            "description": "Description de la première tâche de test pour vérifier l'affichage",
            "bureau": "1B",
            "priority": "Élevée",
            "status": "À faire",
            "due_date": "15/01/2025"
        },
        {
            "title": "Tâche de test 2",
            "description": "Deuxième tâche pour tester les boutons d'actions",
            "bureau": "1B",
            "priority": "Normale",
            "status": "En cours",
            "due_date": "20/01/2025"
        }
    ]
    
    for task in test_tasks:
        table.add_task(task)
    
    window.setCentralWidget(table)
    
    # Style sombre
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
        }
    """)
    
    window.show()
    print("✅ Tableau créé avec succès - Vérifiez que les boutons ✏️ et 🗑️ sont visibles")
    return window

def test_full_dashboard():
    """Test du dashboard complet"""
    print("🧪 Test du dashboard complet...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Créer le dashboard complet
    dashboard = SidebarControlledDashboard()
    dashboard.show()
    
    print("✅ Dashboard complet lancé - Vérifiez :")
    print("   - Les MetricCard ne sont pas coupées")
    print("   - Les boutons d'actions sont visibles dans les tableaux")
    print("   - La navigation entre bureaux fonctionne")
    print("   - Aucune erreur d'animation dans la console")
    
    return dashboard

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests de correction...")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setStyle("Fusion")
    
    # Test 1: MetricCard
    metric_window = test_metric_cards()
    
    # Test 2: Tableau de tâches
    table_window = test_task_table()
    
    # Test 3: Dashboard complet
    dashboard = test_full_dashboard()
    
    print("=" * 50)
    print("🎯 Tous les tests lancés !")
    print("📋 Points à vérifier manuellement :")
    print("   1. Les MetricCard s'affichent complètement sans coupure")
    print("   2. Les boutons d'édition (✏️) et suppression (🗑️) sont visibles")
    print("   3. La hauteur des lignes du tableau est suffisante")
    print("   4. Aucune erreur d'animation dans la console")
    print("   5. La navigation entre bureaux fonctionne correctement")
    
    # Garder les fenêtres ouvertes
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
