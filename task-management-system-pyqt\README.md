# Task Management System - PyQt5

Un système de gestion de tâches moderne développé avec PyQt5, offrant une interface utilisateur intuitive et des fonctionnalités avancées de gestion de projets.

## 🎯 Versions Disponibles

Ce projet contient **deux versions principales** du dashboard :

### 1. **enhanced_sidebar_dashboard.py**
Version complète avec toutes les fonctionnalités avancées :
- Dashboard avec métriques détaillées
- Graphiques interactifs (circulaires, linéaires, barres)
- Sidebar colorée avec navigation par bureaux
- Interface riche et complète

### 2. **dashboard_sidebar_controlled.py**
Version épurée et optimisée :
- Interface simplifiée sans noms de bureaux redondants
- Navigation uniquement via sidebar
- Affichage direct des tâches selon le bureau sélectionné
- Message d'accueil au démarrage
- Interface plus rapide et intuitive

## 🚀 Utilisation

### Version Enhanced (Complète)
```bash
python enhanced_sidebar_dashboard.py
```

### Version Controlled (Épurée)
```bash
python dashboard_sidebar_controlled.py
```

## 📁 Structure Simplifiée

```
task-management-system-pyqt/
├── enhanced_sidebar_dashboard.py     # Version complète
├── dashboard_sidebar_controlled.py  # Version épurée
├── bureau_task_manager.py           # Gestionnaire de tâches
├── chart_widgets.py                 # Composants graphiques
├── icons/                           # Icônes de l'interface
├── resources/                       # Ressources additionnelles
├── requirements.txt                 # Dépendances Python
└── README.md                        # Documentation
```

## 🛠️ Installation

1. **Installer les dépendances**
```bash
pip install PyQt5
```

2. **Lancer une version**
```bash
# Version complète
python enhanced_sidebar_dashboard.py

# Version épurée
python dashboard_sidebar_controlled.py
```

## 🎨 Fonctionnalités Communes

### Système de Bureaux
- **1B** : Gestion Administrative
- **2B** : Ressources Humaines
- **3B** : Finances et Comptabilité
- **4B** : Travaux et Infrastructure
- **5B** : Communication
- **B.GENIE** : Innovation et Technologie
- **C.TRANS** : Transport et Logistique
- **SECRETARIAT** : Secrétariat Général

### Gestion des Tâches
- Création/modification de tâches
- Assignation par bureau
- Gestion des priorités (Faible, Normale, Élevée, Urgente)
- Suivi des statuts (À faire, En cours, Terminée)
- Boutons d'actions (édition, suppression)

### Interface Utilisateur
- Thème sombre moderne
- Sidebar colorée pour navigation
- Design responsive
- Animations fluides

## 🔍 Différences entre les Versions

| Fonctionnalité | Enhanced | Controlled |
|----------------|----------|------------|
| Métriques détaillées | ✅ | ✅ |
| Graphiques interactifs | ✅ | ✅ |
| Navigation sidebar | ✅ | ✅ |
| Noms de bureaux affichés | ✅ | ❌ |
| Message d'accueil | ❌ | ✅ |
| Interface épurée | ❌ | ✅ |
| Onglets de bureaux | ✅ | ❌ |

## 🎯 Recommandations

- **Utilisez `enhanced_sidebar_dashboard.py`** si vous voulez toutes les fonctionnalités
- **Utilisez `dashboard_sidebar_controlled.py`** si vous préférez une interface épurée

## 📝 Notes Techniques

- **PyQt5** : Framework d'interface utilisateur
- **Python 3.x** : Compatible Python 3.6+
- **Thème sombre** : Interface moderne et professionnelle
- **Composants personnalisés** : Widgets optimisés pour la performance

## 🚀 Démarrage Rapide

1. Choisissez votre version préférée
2. Lancez le fichier Python correspondant
3. Cliquez sur un bureau dans la sidebar
4. Créez et gérez vos tâches

L'interface est intuitive et ne nécessite aucune configuration particulière ! 🎉
