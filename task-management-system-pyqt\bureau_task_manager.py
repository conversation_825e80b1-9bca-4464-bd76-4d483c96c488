import sys
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QTabWidget, 
                             QTableWidget, QTableWidgetItem, QHeaderView, QPushButton,
                             QLabel, QFrame, QLineEdit, QComboBox, QDateEdit, QTextEdit,
                             QDialog, QFormLayout, QDialogButtonBox, QMessageBox, QApplication,
                             QMainWindow)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor

class BureauTaskDialog(QDialog):
    """Dialog pour créer/modifier une tâche avec sélection du bureau"""
    
    def __init__(self, task_data=None, current_bureau=None, parent=None):
        super().__init__(parent)
        self.task_data = task_data
        self.current_bureau = current_bureau
        self.setWindowTitle("Nouvelle Tâche" if not task_data else "Modifier Tâche")
        self.setFixedSize(450, 350)
        self.setup_ui()
        self.apply_style()
        
    def setup_ui(self):
        layout = QFormLayout(self)
        
        # Champs du formulaire
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Titre de la tâche...")
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("Description détaillée...")
        
        # Sélection du bureau - NOUVEAU CHAMP
        self.bureau_combo = QComboBox()
        bureaux = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        self.bureau_combo.addItems(bureaux)
        
        # Pré-sélectionner le bureau actuel si fourni
        if self.current_bureau and self.current_bureau in bureaux:
            self.bureau_combo.setCurrentText(self.current_bureau)
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Faible", "Normale", "Élevée", "Urgente"])
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["À faire", "En cours", "Terminée"])
        
        self.due_date = QDateEdit()
        self.due_date.setDate(QDate.currentDate())
        self.due_date.setCalendarPopup(True)
        
        # Remplir les champs si on modifie une tâche
        if self.task_data:
            self.title_edit.setText(self.task_data.get('title', ''))
            self.description_edit.setPlainText(self.task_data.get('description', ''))
            
            bureau = self.task_data.get('bureau', '')
            if bureau in bureaux:
                self.bureau_combo.setCurrentText(bureau)
                
            priority_index = ["Faible", "Normale", "Élevée", "Urgente"].index(
                self.task_data.get('priority', 'Normale'))
            self.priority_combo.setCurrentIndex(priority_index)
            
            status_index = ["À faire", "En cours", "Terminée"].index(
                self.task_data.get('status', 'À faire'))
            self.status_combo.setCurrentIndex(status_index)
        
        layout.addRow("Titre:", self.title_edit)
        layout.addRow("Description:", self.description_edit)
        layout.addRow("Bureau:", self.bureau_combo)  # NOUVEAU CHAMP
        layout.addRow("Priorité:", self.priority_combo)
        layout.addRow("Statut:", self.status_combo)
        layout.addRow("Date d'échéance:", self.due_date)
        
        # Boutons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addRow(buttons)
        
    def apply_style(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #1A1A2E;
                color: white;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit {
                background-color: #2C2C54;
                border: 1px solid #404040;
                border-radius: 5px;
                padding: 8px;
                color: white;
                font-size: 11px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
            QPushButton {
                background-color: #4ECDC4;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5EDDD4;
            }
            QLabel {
                color: #B0B0B0;
                font-weight: bold;
            }
        """)
    
    def get_task_data(self):
        """Retourne les données de la tâche avec le bureau"""
        return {
            'title': self.title_edit.text(),
            'description': self.description_edit.toPlainText(),
            'bureau': self.bureau_combo.currentText(),  # NOUVEAU CHAMP
            'priority': self.priority_combo.currentText(),
            'status': self.status_combo.currentText(),
            'due_date': self.due_date.date().toString("dd/MM/yyyy")
        }

class BureauTaskTable(QTableWidget):
    """Table pour afficher les tâches d'un bureau spécifique"""
    
    task_updated = pyqtSignal(dict)
    task_deleted = pyqtSignal(int)
    
    def __init__(self, bureau_name, parent=None):
        super().__init__(parent)
        self.bureau_name = bureau_name
        self.tasks = []  # Stockage local des tâches
        self.setup_table()
        self.apply_style()
        
    def setup_table(self):
        # Configuration des colonnes
        headers = ["ID", "Titre", "Description", "Priorité", "Statut", "Date d'échéance", "Actions"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # Configuration de l'en-tête
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Titre
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Priorité
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
        
        # Configuration générale
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.verticalHeader().setVisible(False)

        # Hauteur des lignes pour les boutons d'action
        self.verticalHeader().setDefaultSectionSize(45)
        
    def apply_style(self):
        self.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A2E;
                alternate-background-color: #2C2C54;
                color: white;
                gridline-color: #404040;
                border: 1px solid #404040;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #4ECDC4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C2C54;
                color: white;
                padding: 10px;
                border: 1px solid #404040;
                font-weight: bold;
            }
            QScrollBar:vertical {
                background-color: #2C2C54;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #4ECDC4;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #5EDDD4;
            }
        """)
    
    def add_task(self, task_data):
        """Ajoute une tâche à la table si elle appartient à ce bureau"""
        if task_data.get('bureau') != self.bureau_name:
            return  # Ne pas ajouter si ce n'est pas le bon bureau
            
        self.tasks.append(task_data)
        self.refresh_table()
    
    def refresh_table(self):
        """Actualise l'affichage de la table"""
        self.setRowCount(0)  # Vider la table
        
        for i, task_data in enumerate(self.tasks):
            row = self.rowCount()
            self.insertRow(row)
            
            # Données de la tâche
            self.setItem(row, 0, QTableWidgetItem(str(i + 1)))
            self.setItem(row, 1, QTableWidgetItem(task_data['title']))
            
            # Description tronquée
            desc = task_data['description']
            if len(desc) > 50:
                desc = desc[:50] + "..."
            self.setItem(row, 2, QTableWidgetItem(desc))
            
            # Priorité avec couleur
            priority_item = QTableWidgetItem(task_data['priority'])
            priority_colors = {
                "Faible": "#96CEB4",
                "Normale": "#45B7D1", 
                "Élevée": "#FECA57",
                "Urgente": "#FF6B6B"
            }
            priority_item.setBackground(QColor(priority_colors.get(task_data['priority'], "#45B7D1")))
            self.setItem(row, 3, priority_item)
            
            # Statut avec couleur
            status_item = QTableWidgetItem(task_data['status'])
            status_colors = {
                "À faire": "#FF6B6B",
                "En cours": "#FECA57",
                "Terminée": "#4ECDC4"
            }
            status_item.setBackground(QColor(status_colors.get(task_data['status'], "#FF6B6B")))
            self.setItem(row, 4, status_item)
            
            self.setItem(row, 5, QTableWidgetItem(task_data['due_date']))
            
            # Boutons d'action
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)
            actions_layout.setSpacing(5)

            edit_btn = QPushButton("✏️")
            edit_btn.setFixedSize(35, 35)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4ECDC4;
                    border: none;
                    border-radius: 17px;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5EDDD4;
                }
                QPushButton:pressed {
                    background-color: #3BB5AE;
                }
            """)
            edit_btn.clicked.connect(lambda checked, idx=i: self.edit_task(idx))

            delete_btn = QPushButton("🗑️")
            delete_btn.setFixedSize(35, 35)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF6B6B;
                    border: none;
                    border-radius: 17px;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #FF7B7B;
                }
                QPushButton:pressed {
                    background-color: #E55555;
                }
            """)
            delete_btn.clicked.connect(lambda checked, idx=i: self.delete_task(idx))

            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            actions_layout.addStretch()

            self.setCellWidget(row, 6, actions_widget)
    
    def edit_task(self, task_index):
        """Édite une tâche"""
        if task_index < len(self.tasks):
            task_data = self.tasks[task_index]
            dialog = BureauTaskDialog(task_data, self.bureau_name, self)
            if dialog.exec_() == QDialog.Accepted:
                new_data = dialog.get_task_data()
                self.tasks[task_index] = new_data
                self.refresh_table()
                self.task_updated.emit(new_data)
    
    def delete_task(self, task_index):
        """Supprime une tâche"""
        reply = QMessageBox.question(self, "Confirmer", 
                                   "Êtes-vous sûr de vouloir supprimer cette tâche ?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes and task_index < len(self.tasks):
            del self.tasks[task_index]
            self.refresh_table()
            self.task_deleted.emit(task_index)

class BureauTaskManager(QWidget):
    """Gestionnaire principal avec onglets par bureau"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.bureaux = ["1B", "2B", "3B", "4B", "5B", "B.GENIE", "C.TRANS", "SECRETARIAT"]
        self.bureau_tables = {}
        self.all_tasks = []  # Stockage global de toutes les tâches
        self.setup_ui()
        self.apply_style()
        self.add_sample_tasks()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # En-tête avec bouton d'ajout
        header_layout = QHBoxLayout()
        
        title = QLabel("Gestion des Tâches par Bureau")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("color: white; margin-bottom: 15px;")
        
        add_btn = QPushButton("➕ Nouvelle Tâche")
        add_btn.setFixedHeight(35)
        add_btn.clicked.connect(self.add_new_task)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(add_btn)
        
        # Onglets pour chaque bureau
        self.tab_widget = QTabWidget()
        
        # Créer un onglet pour chaque bureau
        for bureau in self.bureaux:
            table = BureauTaskTable(bureau)
            table.task_updated.connect(self.handle_task_update)
            table.task_deleted.connect(self.handle_task_delete)
            self.bureau_tables[bureau] = table
            
            # Couleurs des onglets selon le bureau
            bureau_colors = {
                "1B": "#FF6B6B", "2B": "#4ECDC4", "3B": "#45B7D1", "4B": "#96CEB4",
                "5B": "#FECA57", "B.GENIE": "#FF9FF3", "C.TRANS": "#54A0FF", "SECRETARIAT": "#5F27CD"
            }
            
            self.tab_widget.addTab(table, f"🏢 {bureau}")
        
        layout.addLayout(header_layout)
        layout.addWidget(self.tab_widget)
        
    def apply_style(self):
        self.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #404040;
                border-radius: 8px;
                background-color: #1A1A2E;
            }
            QTabBar::tab {
                background-color: #2C2C54;
                color: white;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #4ECDC4;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5EDDD4;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4ECDC4, stop:1 #3BB5AE);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #5EDDD4, stop:1 #4ECDC4);
            }
        """)
    
    def add_new_task(self):
        """Ajoute une nouvelle tâche"""
        # Déterminer le bureau actuel basé sur l'onglet sélectionné
        current_tab_index = self.tab_widget.currentIndex()
        current_bureau = self.bureaux[current_tab_index] if current_tab_index >= 0 else None
        
        dialog = BureauTaskDialog(current_bureau=current_bureau, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            task_data = dialog.get_task_data()
            if task_data['title'].strip():  # Vérifier que le titre n'est pas vide
                self.all_tasks.append(task_data)
                
                # Ajouter à la table du bureau approprié
                bureau = task_data['bureau']
                if bureau in self.bureau_tables:
                    self.bureau_tables[bureau].add_task(task_data)
    
    def handle_task_update(self, task_data):
        """Gère la mise à jour d'une tâche"""
        # Mettre à jour dans la liste globale si nécessaire
        pass
    
    def handle_task_delete(self, task_index):
        """Gère la suppression d'une tâche"""
        # Mettre à jour dans la liste globale si nécessaire
        pass
    
    def switch_to_bureau(self, bureau_name):
        """Bascule vers l'onglet du bureau spécifié"""
        if bureau_name in self.bureaux:
            index = self.bureaux.index(bureau_name)
            self.tab_widget.setCurrentIndex(index)
    
    def add_sample_tasks(self):
        """Ajoute quelques tâches d'exemple"""
        sample_tasks = [
            {"title": "Rapport mensuel 1B", "description": "Finaliser le rapport du mois", "bureau": "1B", "priority": "Élevée", "status": "À faire", "due_date": "15/01/2025"},
            {"title": "Réunion équipe 2B", "description": "Planifier la réunion hebdomadaire", "bureau": "2B", "priority": "Normale", "status": "En cours", "due_date": "10/01/2025"},
            {"title": "Formation sécurité", "description": "Organiser la formation annuelle", "bureau": "SECRETARIAT", "priority": "Urgente", "status": "À faire", "due_date": "20/01/2025"},
            {"title": "Maintenance véhicules", "description": "Contrôle technique des véhicules", "bureau": "C.TRANS", "priority": "Élevée", "status": "En cours", "due_date": "25/01/2025"},
        ]
        
        for task_data in sample_tasks:
            self.all_tasks.append(task_data)
            bureau = task_data['bureau']
            if bureau in self.bureau_tables:
                self.bureau_tables[bureau].add_task(task_data)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = QMainWindow()
    window.setWindowTitle("Gestionnaire de Tâches par Bureau")
    window.setGeometry(100, 100, 1200, 700)
    
    # Style sombre
    window.setStyleSheet("""
        QMainWindow {
            background-color: #0F0F23;
        }
    """)
    
    task_manager = BureauTaskManager()
    window.setCentralWidget(task_manager)
    
    window.show()
    sys.exit(app.exec_())
